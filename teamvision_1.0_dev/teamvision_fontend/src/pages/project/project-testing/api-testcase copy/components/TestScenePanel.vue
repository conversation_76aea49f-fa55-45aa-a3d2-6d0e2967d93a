<template>
  <div class="test-scene-panel">
    <!-- 左侧场景树 -->
    <div class="scene-tree-section">
      <div class="scene-header">
        <div class="breadcrumb">
          <el-breadcrumb>
            <el-breadcrumb-item>API 测试场景</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <el-input v-model="searchKeyword" placeholder="请输入场景名称进行搜索" size="small" class="search-input"
          @input="handleSearch">
          <i slot="prefix" class="el-icon-search"></i>
        </el-input>
        <div class="action-buttons">
          <el-button type="primary" size="small" icon="el-icon-plus" @click="showNewSceneGroupDialog">
            新建场景集
          </el-button>
          <el-button size="small" icon="el-icon-upload" @click="importScene">
            导入场景
          </el-button>
        </div>
      </div>

      <div class="scene-tree">
        <div class="scene-section">
          <div class="scene-section-header" @click="toggleSceneSection">
            <i class="el-icon-folder-opened"></i>
            <span class="section-title">全部场景</span>
            <span class="scene-count">({{ totalScenes }})</span>
            <div class="section-actions">
              <el-button type="text" size="mini" icon="el-icon-more" @click.stop="showSectionMenu"></el-button>
            </div>
          </div>

          <div v-show="sectionExpanded" class="scene-list">
            <div v-for="scene in filteredScenes" :key="scene.id"
              :class="['scene-item', { active: selectedSceneId === scene.id }]" @click="selectScene(scene)">
              <span class="scene-name">{{ scene.name }}</span>
              <div class="scene-actions">
                <el-button type="text" size="mini" icon="el-icon-edit" @click.stop="editScene(scene)"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete" @click.stop="deleteScene(scene)"></el-button>
                <el-button type="text" size="mini" icon="el-icon-more" @click.stop="showSceneMenu(scene)"></el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧主工作区 -->
    <div class="scene-content-section">
      <!-- 场景列表视图 -->
      <div v-if="currentView === 'list'" class="scene-list-view">
        <scene-list-panel :project-id="projectID" :scenes="scenes" :loading="loading"
          @create-scene="showCreateScenePanel" @edit-scene="showEditScenePanel" @delete-scene="handleDeleteScene"
          @copy-scene="handleCopyScene" @execute-scene="handleExecuteScene" @refresh="loadScenes" />
      </div>

      <!-- 新建场景视图 -->
      <div v-if="currentView === 'create'" class="scene-form-view">
        <scene-form-panel :project-id="projectID" mode="create" @save="handleSaveScene" @cancel="showListView" />
      </div>

      <!-- 编辑场景视图 -->
      <div v-if="currentView === 'edit'" class="scene-form-view">
        <scene-form-panel :project-id="projectID" :scene-data="currentEditScene" mode="edit" @save="handleUpdateScene"
          @cancel="showListView" />
      </div>
    </div>
  </div>
</template>

<script>
import {
  getApiTestScenesApi,
  createApiTestSceneApi,
  updateApiTestSceneApi,
  deleteApiTestSceneApi,
  copyApiTestSceneApi,
  executeApiTestSceneApi,
  handleApiError,
  formatApiResponse
} from '@/api/apiTestCase'
import SceneListPanel from './scene/SceneListPanel.vue'
import SceneFormPanel from './scene/SceneFormPanel.vue'

export default {
  name: 'TestScenePanel',
  components: {
    SceneListPanel,
    SceneFormPanel
  },
  props: {
    projectID: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      // 视图状态
      currentView: 'list', // list, create, edit

      // 场景数据
      scenes: [],
      totalScenes: 0,
      loading: false,

      // 搜索和筛选
      searchKeyword: '',
      filteredScenes: [],

      // 选中状态
      selectedSceneId: null,
      currentEditScene: null,

      // UI状态
      sectionExpanded: true
    }
  },
  mounted() {
    this.loadScenes()
  },
  methods: {
    // 加载场景列表
    async loadScenes() {
      this.loading = true
      try {
        const response = await getApiTestScenesApi(this.projectID)
        const result = formatApiResponse(response)

        if (result.success) {
          this.scenes = result.data.results || []
          this.totalScenes = result.data.count || 0
          this.filterScenes()
        } else {
          this.$message.error(result.message || '加载场景列表失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      } finally {
        this.loading = false
      }
    },

    // 搜索处理
    handleSearch() {
      this.filterScenes()
    },

    // 筛选场景
    filterScenes() {
      if (!this.searchKeyword.trim()) {
        this.filteredScenes = this.scenes
      } else {
        const keyword = this.searchKeyword.toLowerCase()
        this.filteredScenes = this.scenes.filter(scene =>
          scene.name.toLowerCase().includes(keyword) ||
          (scene.description && scene.description.toLowerCase().includes(keyword))
        )
      }
    },

    // 选择场景
    selectScene(scene) {
      this.selectedSceneId = scene.id
      this.$emit('scene-selected', scene)
    },

    // 视图切换
    showListView() {
      this.currentView = 'list'
      this.currentEditScene = null
    },

    showCreateScenePanel() {
      this.currentView = 'create'
    },

    showEditScenePanel(scene) {
      this.currentView = 'edit'
      this.currentEditScene = scene
    },

    // 场景操作
    async handleSaveScene(sceneData) {
      try {
        const response = await createApiTestSceneApi(this.projectID, sceneData)
        const result = formatApiResponse(response)

        if (result.success) {
          this.$message.success('场景创建成功')
          this.showListView()
          this.loadScenes()
        } else {
          this.$message.error(result.message || '创建场景失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    async handleUpdateScene(sceneData) {
      try {
        const response = await updateApiTestSceneApi(
          this.projectID,
          this.currentEditScene.id,
          sceneData
        )
        const result = formatApiResponse(response)

        if (result.success) {
          this.$message.success('场景更新成功')
          this.showListView()
          this.loadScenes()
        } else {
          this.$message.error(result.message || '更新场景失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    async handleDeleteScene(scene) {
      try {
        await this.$confirm(`确定要删除场景 "${scene.name}" 吗？`, '确认删除', {
          type: 'warning'
        })

        const response = await deleteApiTestSceneApi(this.projectID, scene.id)
        const result = formatApiResponse(response)

        if (result.success) {
          this.$message.success('场景删除成功')
          this.loadScenes()
        } else {
          this.$message.error(result.message || '删除场景失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(handleApiError(error))
        }
      }
    },

    async handleCopyScene(scene) {
      try {
        const response = await copyApiTestSceneApi(this.projectID, scene.id, {
          name: `${scene.name} - 副本`
        })
        const result = formatApiResponse(response)

        if (result.success) {
          this.$message.success('场景复制成功')
          this.loadScenes()
        } else {
          this.$message.error(result.message || '复制场景失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    async handleExecuteScene(scene) {
      try {
        const response = await executeApiTestSceneApi(this.projectID, scene.id)
        const result = formatApiResponse(response)

        if (result.success) {
          this.$message.success('场景执行已启动')
          // 可以跳转到执行历史页面或显示执行状态
        } else {
          this.$message.error(result.message || '执行场景失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    // UI操作
    toggleSceneSection() {
      this.sectionExpanded = !this.sectionExpanded
    },

    showNewSceneGroupDialog() {
      this.$message.info('新建场景集功能开发中')
    },

    importScene() {
      this.$message.info('导入场景功能开发中')
    },

    editScene(scene) {
      this.showEditScenePanel(scene)
    },

    deleteScene(scene) {
      this.handleDeleteScene(scene)
    },

    showSectionMenu() {
      this.$message.info('更多操作功能开发中')
    },

    showSceneMenu(scene) {
      console.log('Scene menu for:', scene)
      this.$message.info('场景菜单功能开发中')
    }
  }
}
</script>

<style scoped>
.test-scene-panel {
  display: flex;
  height: 100%;
  background: #fff;
}

/* 左侧场景树 */
.scene-tree-section {
  /* width: 280px; */
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.scene-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
}

.search-input {
  margin-bottom: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.scene-tree {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.scene-section-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.scene-section-header:hover {
  background-color: #f5f5f5;
}

.section-title {
  margin-left: 8px;
  font-weight: 500;
}

.scene-count {
  margin-left: 4px;
  color: #666;
  font-size: 12px;
}

.section-actions {
  margin-left: auto;
}

.scene-list {
  margin-left: 20px;
  margin-top: 4px;
}

.scene-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 2px;
}

.scene-item:hover {
  background-color: #f5f5f5;
}

.scene-item.active {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.scene-name {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.scene-actions {
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  gap: 4px;
}

.scene-item:hover .scene-actions {
  opacity: 1;
}

/* 右侧内容区 */
.scene-content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.scene-list-view,
.scene-form-view {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .scene-tree-section {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .test-scene-panel {
    flex-direction: column;
  }

  .scene-tree-section {
    width: 100%;
    height: 200px;
  }
}
</style>