<template>
  <div class="scene-list-panel">
    <!-- 头部标签和工具栏 -->
    <div class="scene-header">
      <div class="scene-tabs">
        <div class="scene-tab active">
          <span>全部场景</span>
        </div>
        <div class="scene-tab-add" @click="$emit('create-scene')">
          <i class="el-icon-plus"></i>
        </div>
      </div>

      <div class="scene-toolbar">
        <div class="search-section">
          <el-input v-model="searchKeyword" placeholder="请过ID/名称/负责人等信息" size="small" class="search-input"
            @input="handleSearch">
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
        </div>

        <div class="filter-section">
          <span class="filter-label">场景等级:</span>
          <el-select v-model="levelFilter" placeholder="全部数据" size="small" class="filter-select" @change="handleFilter">
            <el-option label="全部数据" value=""></el-option>
            <el-option label="P0" value="P0"></el-option>
            <el-option label="P1" value="P1"></el-option>
            <el-option label="P2" value="P2"></el-option>
          </el-select>
        </div>

        <div class="action-section">
          <el-button size="small" icon="el-icon-delete" @click="clearFilters">清除</el-button>
          <el-button size="small" icon="el-icon-refresh" @click="$emit('refresh')">刷新</el-button>
        </div>
      </div>
    </div>

    <!-- 场景表格 -->
    <div class="scene-content">
      <div class="scene-table-container">
        <el-table :data="filteredScenes" v-loading="loading" stripe style="width: 100%"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50"></el-table-column>

          <el-table-column prop="id" label="ID" width="80" sortable>
            <template slot-scope="scope">
              <span class="scene-id">{{ scope.row.id }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="name" label="场景名称" min-width="200" sortable>
            <template slot-scope="scope">
              <span class="scene-name">{{ scope.row.name }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="level" label="场景等级" width="120" sortable>
            <template slot-scope="scope">
              <el-tag :type="getLevelTagType(scope.row.level)" size="small" class="priority-badge">
                ⭕ {{ scope.row.level || 'P0' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100" sortable>
            <template slot-scope="scope">
              <el-tag :type="getStatusTagType(scope.row.status)" size="small" class="status-badge">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="执行结果" width="150">
            <template slot-scope="scope">
              <el-tag :type="getResultTagType(scope.row.last_result)" size="small" class="result-badge">
                🟢 {{ getResultText(scope.row.last_result) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="tags" label="标签" width="120">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.tags" size="mini" class="tag-badge">
                {{ scope.row.tags }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column prop="environment" label="场景环境" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.environment_name || 'Halo' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template slot-scope="scope">
              <div class="actions-cell">
                <el-button type="text" size="small" class="action-btn edit-btn" @click="$emit('edit-scene', scope.row)">
                  编辑
                </el-button>
                <el-button type="text" size="small" class="action-btn run-btn"
                  @click="$emit('execute-scene', scope.row)">
                  执行
                </el-button>
                <el-button type="text" size="small" class="action-btn copy-btn" @click="$emit('copy-scene', scope.row)">
                  复制
                </el-button>
                <el-dropdown @command="handleCommand" trigger="click">
                  <el-button type="text" size="small" class="action-btn more-btn">
                    <i class="el-icon-more"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item :command="{ action: 'delete', scene: scope.row }">删除</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'export', scene: scope.row }">导出</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'history', scene: scope.row }">执行历史</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <div class="pagination-info">
          共 {{ total }} 条
        </div>
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" layout="sizes, prev, pager, next, jumper" :total="total"
          class="pagination-controls">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SceneListPanel',
  props: {
    projectId: {
      type: Number,
      required: true
    },
    scenes: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 搜索和筛选
      searchKeyword: '',
      levelFilter: '',
      filteredScenes: [],

      // 分页
      currentPage: 1,
      pageSize: 20,
      total: 0,

      // 选中项
      selectedScenes: []
    }
  },
  watch: {
    scenes: {
      handler() {
        this.filterScenes()
      },
      immediate: true
    }
  },
  methods: {
    // 搜索处理
    handleSearch() {
      this.filterScenes()
    },

    // 筛选处理
    handleFilter() {
      this.filterScenes()
    },

    // 筛选场景
    filterScenes() {
      let filtered = [...this.scenes]

      // 关键词搜索
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(scene =>
          scene.name.toLowerCase().includes(keyword) ||
          scene.id.toString().includes(keyword) ||
          (scene.description && scene.description.toLowerCase().includes(keyword))
        )
      }

      // 等级筛选
      if (this.levelFilter) {
        filtered = filtered.filter(scene => scene.level === this.levelFilter)
      }

      this.filteredScenes = filtered
      this.total = filtered.length
    },

    // 清除筛选
    clearFilters() {
      this.searchKeyword = ''
      this.levelFilter = ''
      this.filterScenes()
    },

    // 分页处理
    handleSizeChange(val) {
      this.pageSize = val
      this.handleCurrentChange(1)
    },

    handleCurrentChange(val) {
      this.currentPage = val
    },

    // 选择处理
    handleSelectionChange(selection) {
      this.selectedScenes = selection
    },

    // 下拉菜单命令处理
    handleCommand(command) {
      const { action, scene } = command
      switch (action) {
        case 'delete':
          this.$emit('delete-scene', scene)
          break
        case 'export':
          this.exportScene(scene)
          break
        case 'history':
          this.viewHistory(scene)
          break
      }
    },

    // 导出场景
    exportScene(scene) {
      this.$message.info(`导出场景 "${scene.name}" 功能开发中`)
    },

    // 查看执行历史
    viewHistory(scene) {
      this.$message.info(`查看场景 "${scene.name}" 执行历史功能开发中`)
    },

    // 获取等级标签类型
    getLevelTagType(level) {
      const typeMap = {
        'P0': 'danger',
        'P1': 'warning',
        'P2': 'info'
      }
      return typeMap[level] || 'danger'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'progress': 'warning',
        'completed': 'success',
        'pending': 'info'
      }
      return typeMap[status] || 'warning'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'progress': '进行中',
        'completed': '已完成',
        'pending': '待处理'
      }
      return textMap[status] || '进行中'
    },

    // 获取结果标签类型
    getResultTagType(result) {
      const typeMap = {
        'success': 'success',
        'failed': 'danger',
        'running': 'warning'
      }
      return typeMap[result] || 'success'
    },

    // 获取结果文本
    getResultText(result) {
      const textMap = {
        'success': '成功',
        'failed': '失败',
        'running': '运行中'
      }
      return textMap[result] || '成功'
    }
  }
}
</script>

<style scoped>
.scene-list-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}

/* 头部区域 */
.scene-header {
  border-bottom: 1px solid #e0e0e0;
}

.scene-tabs {
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #f0f0f0;
}

.scene-tab {
  padding: 12px 16px;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s;
}

.scene-tab.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.scene-tab-add {
  padding: 12px 16px;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.scene-tab-add:hover {
  color: #1890ff;
}

.scene-toolbar {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 16px;
  flex-wrap: wrap;
}

.search-section {
  flex: 1;
  min-width: 200px;
}

.search-input {
  max-width: 300px;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.filter-select {
  width: 120px;
}

.action-section {
  display: flex;
  gap: 8px;
}

/* 内容区域 */
.scene-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.scene-table-container {
  flex: 1;
  overflow: auto;
}

/* 表格样式 */
.scene-id {
  font-weight: 500;
  color: #1890ff;
}

.scene-name {
  font-weight: 500;
  color: #333;
}

.priority-badge {
  font-weight: 500;
}

.status-badge,
.result-badge,
.tag-badge {
  font-size: 12px;
}

.actions-cell {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-btn {
  padding: 4px 8px;
  font-size: 12px;
}

.edit-btn {
  color: #1890ff;
}

.run-btn {
  color: #52c41a;
}

.copy-btn {
  color: #fa8c16;
}

.more-btn {
  color: #666;
}

/* 分页区域 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.pagination-info {
  font-size: 14px;
  color: #666;
}

.pagination-controls {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .scene-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-section,
  .filter-section,
  .action-section {
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .scene-tabs {
    padding: 0 8px;
  }

  .scene-toolbar {
    padding: 8px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }
}
</style>
