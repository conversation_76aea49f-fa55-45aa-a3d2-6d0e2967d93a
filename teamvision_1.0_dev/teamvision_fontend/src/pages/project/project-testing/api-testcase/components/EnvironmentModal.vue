<template>
  <el-dialog title="新建环境变量" :visible.sync="dialogVisible" width="600px" :before-close="handleClose">
    <div class="environment-form">
      <el-form :model="environmentData" label-width="80px">
        <el-form-item label="Label">
          <el-input v-model="environmentData.label" placeholder="Environment name (e.g., dev, test, prod)" />
        </el-form-item>
      </el-form>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="Variables" name="variables">

          <div class="variables-section">
            <div class="section-header">
              <span>变量</span>
              <div class="section-actions">
                <el-button @click="addVariable">
                  <i class="el-icon-plus"></i> 添加
                </el-button>
                <el-button @click="pasteVariables">
                  <i class="el-icon-document-copy"></i> 粘贴
                </el-button>
              </div>
            </div>

            <el-table :data="environmentData.variables" style="width: 100%">
              <el-table-column label="Key" width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.key" placeholder="Variable name" />
                </template>
              </el-table-column>
              <el-table-column label="Value" width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.value" placeholder="Variable value" />
                </template>
              </el-table-column>
              <el-table-column label="Description">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.description" placeholder="Description" />
                </template>
              </el-table-column>
              <el-table-column label="Actions" width="80">
                <template slot-scope="scope">
                  <el-button type="text" @click="copyVariable(scope.row)" title="Copy">
                    <i class="el-icon-document-copy"></i>
                  </el-button>
                  <el-button type="text" @click="removeVariable(scope.$index)" title="Delete">
                    <i class="el-icon-delete"></i>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-button type="dashed" style="width: 100%; margin-top: 8px;" @click="addVariable">
              <i class="el-icon-plus"></i> Add Variable
            </el-button>
          </div>
        </el-tab-pane>

        <el-tab-pane label="Secrets" name="secrets">

          <div class="secrets-section">
            <div class="section-header">
              <span>Secrets</span>
              <div class="section-actions">
                <el-button @click="addSecret">
                  <i class="el-icon-plus"></i> Add
                </el-button>
                <el-button @click="pasteSecrets">
                  <i class="el-icon-document-copy"></i> Paste
                </el-button>
              </div>
            </div>

            <el-table :data="environmentData.secrets" style="width: 100%">
              <el-table-column label="Key" width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.key" placeholder="Secret name" />
                </template>
              </el-table-column>
              <el-table-column label="Value" width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.value" type="password" placeholder="Secret value" show-password />
                </template>
              </el-table-column>
              <el-table-column label="Description">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.description" placeholder="Description" />
                </template>
              </el-table-column>
              <el-table-column label="Actions" width="80">
                <template slot-scope="scope">
                  <el-button type="text" @click="copySecret(scope.row)" title="Copy">
                    <i class="el-icon-document-copy"></i>
                  </el-button>
                  <el-button type="text" @click="removeSecret(scope.$index)" title="Delete">
                    <i class="el-icon-delete"></i>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-button type="dashed" style="width: 100%; margin-top: 8px;" @click="addSecret">
              <i class="el-icon-plus"></i> Add Secret
            </el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">Cancel</el-button>
      <el-button type="primary" @click="saveEnvironment">Save</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'EnvironmentModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeTab: 'variables',
      environmentData: {
        label: '',
        variables: [
          {
            key: 'user',
            value: 'zhang',
            description: ''
          },
          {
            key: 'password',
            value: '123456',
            description: ''
          }
        ],
        secrets: [
          {
            key: 'apiKey',
            value: '••••••••••••',
            description: ''
          }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
    },
    addVariable() {
      this.environmentData.variables.push({
        key: '',
        value: '',
        description: ''
      });
    },
    removeVariable(index) {
      this.environmentData.variables.splice(index, 1);
    },
    addSecret() {
      this.environmentData.secrets.push({
        key: '',
        value: '',
        description: ''
      });
    },
    removeSecret(index) {
      this.environmentData.secrets.splice(index, 1);
    },
    copyVariable(variable) {
      navigator.clipboard.writeText(`${variable.key}=${variable.value}`);
    },
    copySecret(secret) {
      navigator.clipboard.writeText(`${secret.key}=${secret.value}`);
    },
    pasteVariables() {
      navigator.clipboard.readText().then(text => {
        const lines = text.split('\n');
        lines.forEach(line => {
          const [key, value] = line.split('=');
          if (key && value) {
            this.environmentData.variables.push({
              key: key.trim(),
              value: value.trim(),
              description: ''
            });
          }
        });
      });
    },
    pasteSecrets() {
      navigator.clipboard.readText().then(text => {
        const lines = text.split('\n');
        lines.forEach(line => {
          const [key, value] = line.split('=');
          if (key && value) {
            this.environmentData.secrets.push({
              key: key.trim(),
              value: value.trim(),
              description: ''
            });
          }
        });
      });
    },
    saveEnvironment() {
      if (!this.environmentData.label.trim()) {
        this.$message.error('请输入环境名称');
        return;
      }

      // 验证变量key是否重复
      const variableKeys = this.environmentData.variables.map(v => v.key).filter(k => k);
      const secretKeys = this.environmentData.secrets.map(s => s.key).filter(k => k);
      const allKeys = [...variableKeys, ...secretKeys];

      if (new Set(allKeys).size !== allKeys.length) {
        this.$message.error('变量名不能重复');
        return;
      }

      this.$emit('save', this.environmentData);
      this.dialogVisible = false;
    }
  }
}
</script>

<style scoped>
.environment-form {
  max-height: 60vh;
  overflow-y: auto;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.variables-section,
.secrets-section {
  margin-top: 16px;
}

.dialog-footer {
  text-align: right;
}
</style>
