<template>
  <div class="response-area">
    <div class="response-header">响应内容
      <div class="response-status">
        <el-tag :type="getStatusType(response.status)" class="status-tag">状态码: {{ response.status_code }} {{
          response.status_text }}
        </el-tag>
        <span class="response-time">响应时间: {{ response.response_time }}ms</span>
        <span class="response-size">响应大小: {{ response.response_size }}</span>
      </div>
      <div class="response-actions">
        <el-button type="text" @click="copyResponse" class="action-btn">
          <i class="el-icon-document-copy"></i> Copy
        </el-button>
        <el-button type="text" @click="saveResponse" class="action-btn">
          <i class="el-icon-download"></i> Save
        </el-button>
      </div>
    </div>

    <div class="response-content">
      <el-tabs v-model="activeResponseTab" class="response-tabs">
        <el-tab-pane label="Body" name="body">
          <div class="response-body">
            <div class="body-header">
              <div class="body-actions">
                <el-button-group class="view-mode-group">
                  <el-button :type="bodyViewMode === 'pretty' ? 'primary' : ''" @click="setBodyViewMode('pretty')"
                    class="mode-btn">
                    Pretty
                  </el-button>
                  <el-button :type="bodyViewMode === 'raw' ? 'primary' : ''" @click="setBodyViewMode('raw')"
                    class="mode-btn">
                    Raw
                  </el-button>
                  <el-button :type="bodyViewMode === 'preview' ? 'primary' : ''" @click="setBodyViewMode('preview')"
                    class="mode-btn">
                    Preview
                  </el-button>
                </el-button-group>
                <el-select v-model="responseLanguage" class="language-select">
                  <el-option label="JSON" value="json"></el-option>
                  <el-option label="XML" value="xml"></el-option>
                  <el-option label="HTML" value="html"></el-option>
                  <el-option label="Text" value="text"></el-option>
                </el-select>
              </div>
            </div>
            <div class="body-content">
              <div v-if="bodyViewMode === 'pretty'" class="pretty-view">
                <pre class="response-json">{{ formattedBody }}</pre>
              </div>
              <div v-else-if="bodyViewMode === 'raw'" class="raw-view">
                <pre class="response-raw">{{ rawBody }}</pre>
              </div>
              <div v-else class="preview-view">
                <div v-html="previewContent" class="html-preview"></div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane name="headers">
          <span slot="label">Headers ({{ Object.keys(response.headers).length }})</span>
          <div class="response-headers">
            <div class="header-table">
              <div class="header-row header-row-title">
                <div class="header-col header-col-key">Key</div>
                <div class="header-col header-col-value">Value</div>
              </div>
              <div v-for="(value, key) in response.headers" :key="key" class="header-row">
                <div class="header-col header-col-key">{{ key }}</div>
                <div class="header-col header-col-value">{{ value }}</div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="Test Results" name="test-results">
          <div class="test-results">
            <div class="test-summary">
              <el-tag type="success" class="test-tag">2 passed</el-tag>
              <el-tag type="danger" class="test-tag">0 failed</el-tag>
            </div>
            <div class="test-list">
              <div class="test-item success">
                <i class="el-icon-check"></i>
                <span>Status code is 200</span>
              </div>
              <div class="test-item success">
                <i class="el-icon-check"></i>
                <span>Response time is less than 500ms</span>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResponseArea',
  props: {
    response: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      activeResponseTab: 'body',
      bodyViewMode: 'pretty',
      responseLanguage: 'json'
    }
  },
  computed: {
    formattedBody() {
      try {
        return JSON.stringify(this.response.body, null, 2);
      } catch (e) {
        return this.response.body;
      }
    },
    rawBody() {
      return typeof this.response.body === 'string'
        ? this.response.body
        : JSON.stringify(this.response.body);
    },
    previewContent() {
      if (typeof this.response.body === 'object') {
        return `<pre>${this.formattedBody}</pre>`;
      }
      return this.response.body;
    }
  },
  methods: {
    getStatusType(status) {
      if (status >= 200 && status < 300) return 'success';
      if (status >= 300 && status < 400) return 'warning';
      if (status >= 400) return 'danger';
      return 'info';
    },
    setBodyViewMode(mode) {
      this.bodyViewMode = mode;
    },
    copyResponse() {
      const text = this.bodyViewMode === 'pretty' ? this.formattedBody : this.rawBody;
      navigator.clipboard.writeText(text).then(() => {
        this.$message.success('Response copied to clipboard');
      }).catch(() => {
        this.$message.error('Failed to copy response');
      });
    },
    saveResponse() {
      const blob = new Blob([this.formattedBody], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'response.json';
      a.click();
      URL.revokeObjectURL(url);
    }
  }
}
</script>

<style scoped>
.response-area {
  border-top: 2px solid #e8e8e8;
  background: white;
  display: flex;
  flex-direction: column;
  height: 45vh;
  min-height: 250px;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.response-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-tag {
  font-weight: 600;
  font-size: 12px;
  padding: 1px 4px;
  height: 16px;
  line-height: 14px;
}

.response-time {
  font-size: 11px;
  color: #606266;
  font-weight: 500;
}

.response-size {
  font-size: 11px;
  color: #909399;
}

.response-actions {
  display: flex;
  gap: 6px;
}

.action-btn {
  color: #1890ff;
  font-size: 11px;
  padding: 2px 6px;
  height: 20px;
  line-height: 16px;
}

.response-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.response-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.response-tabs>>>.el-tabs__header {
  margin: 0;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.response-tabs>>>.el-tabs__item {
  height: 32px;
  line-height: 32px;
  padding: 0 16px;
  font-size: 11px;
  font-weight: 500;
}

.response-tabs>>>.el-tabs__content {
  flex: 1;
  padding: 0;
  overflow: auto;
}

.response-tabs>>>.el-tab-pane {
  height: 100%;
  padding: 12px;
}

/* Body 样式 */
.response-body {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.body-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
}

.body-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.view-mode-group {
  display: flex;
}

.mode-btn {
  font-size: 10px;
  padding: 2px 8px;
  height: 22px;
  line-height: 18px;
}

.language-select {
  width: 80px;
}

.language-select>>>.el-input--mini .el-input__inner {
  height: 22px;
  line-height: 22px;
  font-size: 10px;
  padding: 0 6px;
}

.body-content {
  flex: 1;
  overflow: auto;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.pretty-view,
.raw-view {
  height: 100%;
  overflow: auto;
}

.response-json,
.response-raw {
  margin: 0;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  font-size: 10px;
  line-height: 1.5;
  color: #303133;
  background: #fafafa;
  white-space: pre-wrap;
  word-break: break-all;
  min-height: 100%;
}

.preview-view {
  padding: 12px;
  height: 100%;
  overflow: auto;
}

.html-preview {
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  font-size: 10px;
  line-height: 1.5;
}

/* Headers 样式 */
.response-headers {
  height: 100%;
  overflow: auto;
}

.header-table {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  background: white;
}

.header-row {
  display: flex;
  border-bottom: 1px solid #f5f5f5;
  min-height: 28px;
}

.header-row:last-child {
  border-bottom: none;
}

.header-row-title {
  background: #fafafa;
  font-weight: 600;
  font-size: 11px;
  color: #606266;
}

.header-col {
  padding: 6px 10px;
  font-size: 11px;
  display: flex;
  align-items: center;
}

.header-col-key {
  flex: 0 0 28%;
  border-right: 1px solid #f5f5f5;
  font-weight: 500;
  color: #303133;
}

.header-col-value {
  flex: 1;
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  color: #606266;
  word-break: break-all;
  font-size: 10px;
}

/* Test Results 样式 */
.test-results {
  height: 100%;
}

.test-summary {
  display: flex;
  gap: 6px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.test-tag {
  font-size: 10px;
  padding: 1px 4px;
  height: 16px;
  line-height: 14px;
}

.test-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.test-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  border-radius: 3px;
  font-size: 11px;
}

.test-item.success {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.test-item.error {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

.test-item i {
  font-size: 12px;
}

/* 滚动条优化 */
.body-content::-webkit-scrollbar,
.response-headers::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.body-content::-webkit-scrollbar-track,
.response-headers::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.body-content::-webkit-scrollbar-thumb,
.response-headers::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.body-content::-webkit-scrollbar-thumb:hover,
.response-headers::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .response-header {
    flex-direction: column;
    gap: 6px;
    align-items: flex-start;
    padding: 6px 8px;
  }

  .body-header {
    flex-direction: column;
    gap: 6px;
    align-items: flex-start;
  }

  .body-actions {
    width: 100%;
    justify-content: space-between;
  }

  .response-area {
    height: 40vh;
    min-height: 200px;
  }

  .response-json,
  .response-raw {
    font-size: 9px;
    padding: 8px;
  }

  .header-col-key {
    flex: 0 0 35%;
  }
}
</style>
