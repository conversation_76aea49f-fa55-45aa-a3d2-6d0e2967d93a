<template>
  <div class="scene-form-panel">
    <!-- 头部标签和操作栏 -->
    <div class="scene-form-header">
      <div class="scene-form-tabs">
        <div class="scene-form-tab" @click="$emit('cancel')">
          <span>全部场景</span>
        </div>
        <div class="scene-form-tab active">
          <span>{{ mode === 'create' ? '新建场景' : sceneForm.name }}</span>
          <el-button type="text" size="mini" icon="el-icon-close" @click="$emit('cancel')"></el-button>
        </div>
        <div class="scene-form-tab add-tab">
          <i class="el-icon-plus"></i>
        </div>
      </div>
      <div class="scene-form-actions">
        <el-button icon="el-icon-view" size="small" title="预览">
          <i class="el-icon-view"></i>
        </el-button>
        <el-button type="primary" size="small" @click="executeScene" :disabled="mode === 'create'">
          服务端执行
        </el-button>
        <el-button type="success" size="small" @click="saveScene">
          保存
        </el-button>
      </div>
    </div>

    <!-- 场景基本信息（编辑模式显示） -->
    <div v-if="mode === 'edit'" class="basic-info-section">
      <div class="basic-info-row">
        <span class="basic-info-label">{{ getStatusText(sceneForm.status) }}</span>
        <span class="priority-indicator">
          <span class="priority-dot" :class="sceneForm.level"></span>
          <span>{{ sceneForm.level || 'P0' }}</span>
        </span>
        <span class="basic-info-value">[{{ sceneForm.id }}] {{ sceneForm.name }}</span>
        <el-button type="text" size="mini" icon="el-icon-star-off"></el-button>
        <el-button type="text" size="mini" icon="el-icon-document"></el-button>
      </div>
      <div class="basic-info-row">
        <span class="basic-info-label">标签</span>
        <span class="basic-info-value">{{ sceneForm.tags || '-' }}</span>
      </div>
      <div class="basic-info-row">
        <span class="basic-info-label">描述</span>
        <span class="basic-info-value">{{ sceneForm.description || '-' }}</span>
      </div>
    </div>

    <!-- 表单内容标签页 -->
    <div class="scene-form-tabs-content">
      <div v-for="tab in formTabs" :key="tab.key" :class="['scene-form-tab-item', { active: activeTab === tab.key }]"
        @click="switchTab(tab.key)">
        {{ tab.label }}
      </div>
    </div>

    <!-- 表单主体 -->
    <div class="scene-form-body">
      <!-- 步骤管理 -->
      <div v-if="activeTab === 'steps'" class="scene-steps-content">
        <scene-steps-manager :project-id="projectId" :scene-id="sceneForm.id" :steps="sceneForm.steps"
          @steps-change="handleStepsChange" />
      </div>

      <!-- 参数配置 -->
      <div v-if="activeTab === 'params'" class="scene-params-content">
        <scene-params-manager :global-variables="sceneForm.global_variables" :global-headers="sceneForm.global_headers"
          @params-change="handleParamsChange" />
      </div>

      <!-- 前/后置脚本 -->
      <div v-if="activeTab === 'scripts'" class="scene-scripts-content">
        <scene-scripts-manager :pre-script="sceneForm.pre_script" :post-script="sceneForm.post_script"
          @scripts-change="handleScriptsChange" />
      </div>

      <!-- 断言配置 -->
      <div v-if="activeTab === 'assertions'" class="scene-assertions-content">
        <scene-assertions-manager :assertions="sceneForm.assertions" @assertions-change="handleAssertionsChange" />
      </div>

      <!-- 执行历史 -->
      <div v-if="activeTab === 'execution'" class="scene-execution-content">
        <scene-execution-history :project-id="projectId" :scene-id="sceneForm.id" />
      </div>

      <!-- 变更历史 -->
      <div v-if="activeTab === 'changes'" class="scene-changes-content">
        <scene-change-history :project-id="projectId" :scene-id="sceneForm.id" />
      </div>

      <!-- 设置 -->
      <div v-if="activeTab === 'settings'" class="scene-settings-content">
        <scene-settings-manager :settings="sceneForm.settings" @settings-change="handleSettingsChange" />
      </div>

      <!-- 右侧表单 -->
      <div class="scene-form-sidebar">
        <el-form :model="sceneForm" :rules="formRules" ref="sceneForm" label-width="100px">
          <el-form-item label="场景名称" prop="name" required>
            <el-input v-model="sceneForm.name" placeholder="请输入场景名称"></el-input>
          </el-form-item>

          <el-form-item label="所属模块">
            <el-select v-model="sceneForm.module" placeholder="未规划场景" style="width: 100%">
              <el-option label="未规划场景" value=""></el-option>
              <el-option label="登录模块" value="login"></el-option>
              <el-option label="用户模块" value="user"></el-option>
              <el-option label="文章模块" value="article"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="场景等级">
            <el-radio-group v-model="sceneForm.level">
              <el-radio label="P0">P0</el-radio>
              <el-radio label="P1">P1</el-radio>
              <el-radio label="P2">P2</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="场景状态">
            <el-select v-model="sceneForm.status" style="width: 100%">
              <el-option label="进行中" value="progress"></el-option>
              <el-option label="已完成" value="completed"></el-option>
              <el-option label="待处理" value="pending"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="标签">
            <el-input v-model="sceneForm.tags" placeholder="添加标签，回车键确定"></el-input>
          </el-form-item>

          <el-form-item label="描述">
            <el-input type="textarea" v-model="sceneForm.description" placeholder="请对场景进行描述" :rows="4"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import SceneStepsManager from './SceneStepsManager.vue'
import SceneParamsManager from './SceneParamsManager.vue'
import SceneScriptsManager from './SceneScriptsManager.vue'
import SceneAssertionsManager from './SceneAssertionsManager.vue'
import SceneExecutionHistory from './SceneExecutionHistory.vue'
import SceneChangeHistory from './SceneChangeHistory.vue'
import SceneSettingsManager from './SceneSettingsManager.vue'

export default {
  name: 'SceneFormPanel',
  components: {
    SceneStepsManager,
    SceneParamsManager,
    SceneScriptsManager,
    SceneAssertionsManager,
    SceneExecutionHistory,
    SceneChangeHistory,
    SceneSettingsManager
  },
  props: {
    projectId: {
      type: Number,
      required: true
    },
    sceneData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'create', // create, edit
      validator: value => ['create', 'edit'].includes(value)
    }
  },
  data() {
    return {
      activeTab: 'steps',
      sceneForm: {
        name: '',
        module: '',
        level: 'P0',
        status: 'progress',
        tags: '',
        description: '',
        steps: [],
        global_variables: {},
        global_headers: {},
        pre_script: '',
        post_script: '',
        assertions: [],
        settings: {}
      },
      formRules: {
        name: [
          { required: true, message: '请输入场景名称', trigger: 'blur' },
          { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    formTabs() {
      const baseTabs = [
        { key: 'steps', label: '步骤' },
        { key: 'params', label: '参数' },
        { key: 'scripts', label: '前/后置' },
        { key: 'assertions', label: '断言' },
        { key: 'settings', label: '设置' }
      ]

      if (this.mode === 'edit') {
        baseTabs.splice(4, 0,
          { key: 'execution', label: '执行历史' },
          { key: 'changes', label: '变更历史' }
        )
      }

      return baseTabs
    }
  },
  watch: {
    sceneData: {
      handler(newData) {
        if (newData && Object.keys(newData).length > 0) {
          this.sceneForm = { ...this.sceneForm, ...newData }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 切换标签页
    switchTab(tabKey) {
      this.activeTab = tabKey
    },

    // 保存场景
    saveScene() {
      this.$refs.sceneForm.validate((valid) => {
        if (valid) {
          this.$emit('save', this.sceneForm)
        }
      })
    },

    // 执行场景
    executeScene() {
      if (this.mode === 'edit' && this.sceneForm.id) {
        this.$emit('execute', this.sceneForm)
      }
    },

    // 处理步骤变更
    handleStepsChange(steps) {
      this.sceneForm.steps = steps
    },

    // 处理参数变更
    handleParamsChange(params) {
      this.sceneForm.global_variables = params.variables
      this.sceneForm.global_headers = params.headers
    },

    // 处理脚本变更
    handleScriptsChange(scripts) {
      this.sceneForm.pre_script = scripts.preScript
      this.sceneForm.post_script = scripts.postScript
    },

    // 处理断言变更
    handleAssertionsChange(assertions) {
      this.sceneForm.assertions = assertions
    },

    // 处理设置变更
    handleSettingsChange(settings) {
      this.sceneForm.settings = settings
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'progress': '进行中',
        'completed': '已完成',
        'pending': '待处理'
      }
      return textMap[status] || '进行中'
    }
  }
}
</script>

<style scoped>
.scene-form-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}

/* 头部区域 */
.scene-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #e0e0e0;
  background: #fafafa;
}

.scene-form-tabs {
  display: flex;
  align-items: center;
}

.scene-form-tab {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s;
  gap: 8px;
}

.scene-form-tab.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.scene-form-tab:hover {
  color: #1890ff;
}

.add-tab {
  color: #999;
}

.scene-form-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 基本信息区域 */
.basic-info-section {
  padding: 16px;
  background: #f9f9f9;
  border-bottom: 1px solid #e0e0e0;
}

.basic-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 12px;
}

.basic-info-row:last-child {
  margin-bottom: 0;
}

.basic-info-label {
  font-size: 12px;
  color: #666;
  background: #e6f7ff;
  padding: 2px 8px;
  border-radius: 4px;
  min-width: 60px;
  text-align: center;
}

.priority-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.priority-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff4d4f;
}

.priority-dot.P1 {
  background: #faad14;
}

.priority-dot.P2 {
  background: #52c41a;
}

.basic-info-value {
  font-size: 14px;
  color: #333;
  flex: 1;
}

/* 标签页内容 */
.scene-form-tabs-content {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  background: #fff;
}

.scene-form-tab-item {
  padding: 12px 20px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.scene-form-tab-item.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.scene-form-tab-item:hover {
  color: #1890ff;
}

/* 表单主体 */
.scene-form-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.scene-steps-content,
.scene-params-content,
.scene-scripts-content,
.scene-assertions-content,
.scene-execution-content,
.scene-changes-content,
.scene-settings-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 右侧表单 */
.scene-form-sidebar {
  width: 300px;
  padding: 16px;
  border-left: 1px solid #e0e0e0;
  background: #fafafa;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .scene-form-body {
    flex-direction: column;
  }

  .scene-form-sidebar {
    width: 100%;
    border-left: none;
    border-top: 1px solid #e0e0e0;
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .scene-form-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .scene-form-tabs {
    justify-content: center;
  }

  .scene-form-actions {
    justify-content: center;
  }

  .basic-info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .scene-form-tabs-content {
    flex-wrap: wrap;
  }

  .scene-form-tab-item {
    padding: 8px 12px;
    font-size: 12px;
  }
}
</style>
