#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试场景接口测试脚本

使用方法:
python test_scene_api_endpoints.py

注意：运行前请确保：
1. Django服务已启动
2. 数据库迁移已完成
3. 修改下面的配置参数
"""

import requests
import json
import time
from datetime import datetime


class ApiTestSceneEndpointTester:
    def __init__(self, base_url="http://localhost:8000", project_id=1):
        self.base_url = base_url
        self.project_id = project_id
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        # 如果需要认证，在这里添加
        # self.session.auth = ('username', 'password')
        # 或者设置token
        # self.session.headers.update({'Authorization': 'Bearer your-token'})

    def test_all_endpoints(self):
        """测试所有场景相关的API端点"""
        print("=" * 60)
        print("API测试场景接口测试")
        print("=" * 60)
        print(f"测试服务器: {self.base_url}")
        print(f"项目ID: {self.project_id}")
        print(f"开始时间: {datetime.now()}")
        print()

        # 测试场景管理接口
        scene_id = self.test_scene_management()
        
        if scene_id:
            # 测试场景步骤管理接口
            step_id = self.test_scene_step_management(scene_id)
            
            # 测试场景执行接口
            history_id = self.test_scene_execution(scene_id)
            
            if history_id:
                # 测试执行历史接口
                self.test_scene_history(history_id)

        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)

    def test_scene_management(self):
        """测试场景管理接口"""
        print("=== 测试场景管理接口 ===")
        
        # 1. 获取场景列表
        url = f"{self.base_url}/api/project/{self.project_id}/api-test/scenes/"
        response = self.session.get(url)
        print(f"GET {url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✓ 获取场景列表成功")
        else:
            print(f"✗ 获取场景列表失败: {response.text}")
            
        # 2. 创建场景
        create_data = {
            "name": "测试场景",
            "description": "这是一个测试场景",
            "module": "测试模块",
            "level": "P0",
            "status": "progress",
            "tags": "自动化测试",
            "global_variables": {
                "base_url": "https://api.example.com",
                "api_version": "v1"
            },
            "global_headers": {
                "Content-Type": "application/json"
            },
            "settings": {
                "timeout": 30,
                "retry_count": 3
            },
            "is_parallel": False
        }
        
        response = self.session.post(url, json=create_data)
        print(f"POST {url}")
        print(f"Status: {response.status_code}")
        
        scene_id = None
        if response.status_code == 201:
            result = response.json()
            scene_id = result.get('result', {}).get('id') or result.get('id')
            print(f"✓ 创建场景成功，ID: {scene_id}")
        else:
            print(f"✗ 创建场景失败: {response.text}")
            return None
            
        # 3. 获取场景详情
        if scene_id:
            detail_url = f"{url}{scene_id}/"
            response = self.session.get(detail_url)
            print(f"GET {detail_url}")
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                print("✓ 获取场景详情成功")
            else:
                print(f"✗ 获取场景详情失败: {response.text}")
                
        # 4. 更新场景
        if scene_id:
            update_data = {
                "name": "更新后的测试场景",
                "description": "这是一个更新后的测试场景",
                "status": "completed"
            }
            response = self.session.put(detail_url, json=update_data)
            print(f"PUT {detail_url}")
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                print("✓ 更新场景成功")
            else:
                print(f"✗ 更新场景失败: {response.text}")
                
        # 5. 复制场景
        if scene_id:
            copy_url = f"{detail_url}copy/"
            response = self.session.post(copy_url)
            print(f"POST {copy_url}")
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                print("✓ 复制场景成功")
            else:
                print(f"✗ 复制场景失败: {response.text}")
        
        print()
        return scene_id

    def test_scene_step_management(self, scene_id):
        """测试场景步骤管理接口"""
        if not scene_id:
            print("跳过场景步骤测试（没有有效的场景ID）")
            return None
            
        print("=== 测试场景步骤管理接口 ===")
        
        # 1. 获取场景步骤列表
        url = f"{self.base_url}/api/project/{self.project_id}/api-test/scenes/{scene_id}/steps/"
        response = self.session.get(url)
        print(f"GET {url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✓ 获取场景步骤列表成功")
        else:
            print(f"✗ 获取场景步骤列表失败: {response.text}")
            
        # 2. 创建API步骤
        create_data = {
            "step_name": "测试API步骤",
            "description": "这是一个测试API步骤",
            "step_type": "api",
            "is_enabled": True,
            "method": "GET",
            "url": "{{base_url}}/{{api_version}}/users",
            "headers": {
                "Authorization": "Bearer {{token}}"
            },
            "query_params": {
                "page": "1",
                "limit": "10"
            },
            "variable_extracts": [
                {
                    "name": "user_id",
                    "type": "jsonpath",
                    "expression": "data[0].id"
                }
            ],
            "step_assertions": [
                {
                    "type": "status_code",
                    "operator": "equals",
                    "expected": 200
                }
            ]
        }
        
        response = self.session.post(url, json=create_data)
        print(f"POST {url}")
        print(f"Status: {response.status_code}")
        
        step_id = None
        if response.status_code == 201:
            result = response.json()
            step_id = result.get('result', {}).get('id') or result.get('id')
            print(f"✓ 创建API步骤成功，ID: {step_id}")
        else:
            print(f"✗ 创建API步骤失败: {response.text}")
            
        # 3. 创建等待步骤
        wait_step_data = {
            "step_name": "等待步骤",
            "description": "等待1秒",
            "step_type": "wait",
            "is_enabled": True,
            "wait_time": 1000
        }
        
        response = self.session.post(url, json=wait_step_data)
        print(f"POST {url} (等待步骤)")
        print(f"Status: {response.status_code}")
        if response.status_code == 201:
            print("✓ 创建等待步骤成功")
        else:
            print(f"✗ 创建等待步骤失败: {response.text}")
            
        # 4. 更新步骤顺序
        if step_id:
            order_url = f"{url}order/"
            order_data = {
                "step_orders": {
                    str(step_id): 1
                }
            }
            response = self.session.put(order_url, json=order_data)
            print(f"PUT {order_url}")
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                print("✓ 更新步骤顺序成功")
            else:
                print(f"✗ 更新步骤顺序失败: {response.text}")
        
        print()
        return step_id

    def test_scene_execution(self, scene_id):
        """测试场景执行接口"""
        if not scene_id:
            print("跳过场景执行测试（没有有效的场景ID）")
            return None
            
        print("=== 测试场景执行接口 ===")
        
        # 1. 执行场景
        url = f"{self.base_url}/api/project/{self.project_id}/api-test/scenes/{scene_id}/execute/"
        execute_data = {
            "environment_id": None,
            "override_variables": {
                "base_url": "https://jsonplaceholder.typicode.com",
                "api_version": "",
                "token": "test-token"
            }
        }
        
        response = self.session.post(url, json=execute_data)
        print(f"POST {url}")
        print(f"Status: {response.status_code}")
        
        history_id = None
        if response.status_code == 200:
            result = response.json()
            history_id = result.get('result', {}).get('history_id')
            print(f"✓ 执行场景成功，历史ID: {history_id}")
        else:
            print(f"✗ 执行场景失败: {response.text}")
            
        # 2. 等待执行完成（简单等待）
        if history_id:
            print("等待场景执行完成...")
            time.sleep(3)
            
        # 3. 停止执行（测试停止功能）
        if history_id:
            stop_url = f"{self.base_url}/api/project/{self.project_id}/api-test/scenes/{scene_id}/stop/"
            stop_data = {
                "history_id": history_id
            }
            response = self.session.post(stop_url, json=stop_data)
            print(f"POST {stop_url}")
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                print("✓ 停止场景执行成功")
            else:
                print(f"✗ 停止场景执行失败: {response.text}")
        
        print()
        return history_id

    def test_scene_history(self, history_id):
        """测试场景执行历史接口"""
        if not history_id:
            print("跳过执行历史测试（没有有效的历史ID）")
            return
            
        print("=== 测试场景执行历史接口 ===")
        
        # 1. 获取执行历史列表
        url = f"{self.base_url}/api/project/{self.project_id}/api-test/scenes/history/"
        response = self.session.get(url)
        print(f"GET {url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✓ 获取执行历史列表成功")
        else:
            print(f"✗ 获取执行历史列表失败: {response.text}")
            
        # 2. 获取执行历史详情
        detail_url = f"{url}{history_id}/"
        response = self.session.get(detail_url)
        print(f"GET {detail_url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✓ 获取执行历史详情成功")
        else:
            print(f"✗ 获取执行历史详情失败: {response.text}")
            
        # 3. 获取步骤执行记录
        steps_url = f"{detail_url}steps/"
        response = self.session.get(steps_url)
        print(f"GET {steps_url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✓ 获取步骤执行记录成功")
        else:
            print(f"✗ 获取步骤执行记录失败: {response.text}")
        
        print()


if __name__ == "__main__":
    # 配置参数
    BASE_URL = "http://localhost:8000"  # 修改为实际的服务器地址
    PROJECT_ID = 1  # 修改为实际的项目ID
    
    # 创建测试器并运行测试
    tester = ApiTestSceneEndpointTester(BASE_URL, PROJECT_ID)
    tester.test_all_endpoints()
