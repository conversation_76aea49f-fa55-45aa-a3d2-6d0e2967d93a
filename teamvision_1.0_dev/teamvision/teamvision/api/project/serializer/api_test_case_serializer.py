# coding=utf-8
"""
Created on 2025-09-15

@author: zhangpeng
"""

from rest_framework import serializers
from business.auth_user.user_service import UserService
from teamvision.project.models import ApiTestCollection, ApiTestCase, ApiTestEnvironment, ApiTestHistory


class ApiTestCollectionTreeSerializer(serializers.ModelSerializer):
    """API测试集合树形序列化器（用于递归）"""
    children = serializers.SerializerMethodField()
    case_count = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_children(self, obj):
        if obj.is_folder:
            children = ApiTestCollection.objects.filter(parent=obj.id, IsActive=True)
            return ApiTestCollectionTreeSerializer(children, many=True).data
        return []

    def get_case_count(self, obj):
        return ApiTestCase.objects.filter(collection=obj, IsActive=True).count()

    class Meta:
        model = ApiTestCollection
        fields = ['id', 'name', 'description', 'is_folder', 'parent', 'sort_order',
                 'children', 'case_count', 'CreationTime']


class ApiTestCollectionSerializer(serializers.ModelSerializer):
    """API测试集合序列化器"""
    children = serializers.SerializerMethodField()
    case_count = serializers.SerializerMethodField()
    creator_name = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_children(self, obj):
        if obj.is_folder:
            children = ApiTestCollection.objects.filter(parent=obj.id, IsActive=True)
            return ApiTestCollectionTreeSerializer(children, many=True).data
        return []

    def get_case_count(self, obj):
        return ApiTestCase.objects.filter(collection=obj, IsActive=True).count()

    def get_creator_name(self, obj):
        return UserService.get_name_by_id(obj.creator)

    class Meta:
        model = ApiTestCollection
        fields = ['id', 'project_id', 'name', 'description', 'parent', 'creator',
                 'creator_name', 'is_folder', 'sort_order', 'children', 'case_count',
                 'CreationTime', 'IsActive']
        read_only_fields = ['id', 'CreationTime', 'IsActive', 'children', 'case_count', 'creator_name']

    def create(self, validated_data):
        # 设置创建人
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            validated_data['creator'] = request.user.id
        return super().create(validated_data)


class ApiTestCollectionTestCaseTreeSerializer(serializers.ModelSerializer):
    """
      API测试集合, 包含测试用例序列化器
    """
    children = serializers.SerializerMethodField()
    case_count = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_children(self, obj):
        children = list()
        if obj.is_folder:
            children_collection = ApiTestCollection.objects.filter(parent=obj.id, IsActive=True)
            children.extend(ApiTestCollectionTestCaseTreeSerializer(children_collection, many=True).data)
        
            children_case = ApiTestCase.objects.filter(collection_id=obj.id, IsActive=True)
            children.extend(ApiTestCaseTreeSerializer(children_case, many=True).data)

        return children

    def get_case_count(self, obj):
        return ApiTestCase.objects.filter(collection=obj, IsActive=True).count()


    class Meta:
        model = ApiTestCollection
        fields = ['id', 'project_id', 'name', 'parent', 'creator', 'is_folder', 'children', 'case_count', 'CreationTime']
        read_only_fields = ['id', 'CreationTime', 'children', 'case_count']


class ApiTestCaseTreeSerializer(serializers.ModelSerializer):
    """API测试用例序列化器"""
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = ApiTestCase
        fields = ['id', 'collection',  'method', 'name', 'creator', 'CreationTime', ]
        read_only_fields = ['id', 'CreationTime']

    

class ApiTestCaseSerializer(serializers.ModelSerializer):
    """API测试用例序列化器"""
    collection_name = serializers.CharField(source='collection.name', read_only=True)
    creator_name = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_creator_name(self, obj):
        return UserService.get_name_by_id(obj.creator)

    class Meta:
        model = ApiTestCase
        fields = ['id', 'collection', 'collection_name', 'name', 'description',
                 'method', 'url', 'headers', 'query_params', 'path_variables',
                 'body_type', 'body_data', 'auth_type', 'auth_config',
                 'pre_request_script', 'post_request_script', 'test_assertions',
                 'creator', 'creator_name', 'sort_order', 'CreationTime', 'IsActive']
        read_only_fields = ['id', 'CreationTime', 'IsActive', 'collection_name', 'creator_name']

    def create(self, validated_data):
        # 设置创建人
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            validated_data['creator'] = request.user.id
        return super().create(validated_data)

    def validate_method(self, value):
        """验证HTTP方法"""
        allowed_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']
        if value.upper() not in allowed_methods:
            raise serializers.ValidationError(f"HTTP方法必须是以下之一: {', '.join(allowed_methods)}")
        return value.upper()

    def validate_body_type(self, value):
        """验证请求体类型"""
        allowed_types = ['none', 'raw', 'form-data', 'x-www-form-urlencoded', 'binary']
        if value not in allowed_types:
            raise serializers.ValidationError(f"请求体类型必须是以下之一: {', '.join(allowed_types)}")
        return value

    def validate_auth_type(self, value):
        """验证认证类型"""
        allowed_types = ['none', 'basic', 'bearer', 'api-key', 'oauth2']
        if value not in allowed_types:
            raise serializers.ValidationError(f"认证类型必须是以下之一: {', '.join(allowed_types)}")
        return value


class ApiTestEnvironmentSerializer(serializers.ModelSerializer):
    """API测试环境序列化器"""
    creator_name = serializers.SerializerMethodField()
    variable_count = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_creator_name(self, obj):
        return UserService.get_name_by_id(obj.creator)

    def get_variable_count(self, obj):
        return len(obj.variables) + len(obj.secrets)

    class Meta:
        model = ApiTestEnvironment
        fields = ['id', 'project_id', 'name', 'description', 'variables', 'secrets',
                 'is_global', 'creator', 'creator_name', 'variable_count',
                 'CreationTime', 'IsActive']
        read_only_fields = ['id', 'CreationTime', 'IsActive', 'creator_name', 'variable_count']

    def create(self, validated_data):
        # 设置创建人
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            validated_data['creator'] = request.user.id
        return super().create(validated_data)

    def to_representation(self, instance):
        """自定义序列化输出，隐藏敏感变量的值"""
        data = super().to_representation(instance)
        # 在列表视图中隐藏敏感变量的具体值
        if self.context.get('hide_secrets', False):
            secrets = data.get('secrets', {})
            data['secrets'] = {key: '***' for key in secrets.keys()}
        return data


class ApiTestHistorySerializer(serializers.ModelSerializer):
    """API测试历史序列化器"""
    test_case_name = serializers.CharField(source='test_case.name', read_only=True)
    executor_name = serializers.SerializerMethodField()
    duration_display = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()

    def get_executor_name(self, obj):
        return UserService.get_name_by_id(obj.executor)

    def get_duration_display(self, obj):
        """格式化响应时间显示"""
        if obj.response_time is None:
            return "N/A"
        if obj.response_time < 1000:
            return f"{obj.response_time}ms"
        else:
            return f"{obj.response_time / 1000:.2f}s"

    def get_status_display(self, obj):
        """格式化状态显示"""
        if obj.response_status is None:
            return "Failed"
        return f"{obj.response_status}"

    class Meta:
        model = ApiTestHistory
        fields = ['id', 'project_id', 'test_case', 'test_case_name',
                 'request_snapshot', 'response_status', 'response_headers',
                 'response_body', 'response_time', 'response_size',
                 'test_results', 'is_success', 'error_message',
                 'executor', 'executor_name', 'executed_at',
                 'duration_display', 'status_display']
        read_only_fields = ['id', 'executed_at', 'test_case_name', 'executor_name',
                           'duration_display', 'status_display']

    def create(self, validated_data):
        # 设置执行人
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            validated_data['executor'] = request.user.id
        return super().create(validated_data)


class ApiTestCaseExecuteSerializer(serializers.Serializer):
    """API测试用例执行请求序列化器"""
    environment_id = serializers.IntegerField(required=False, allow_null=True,
                                             help_text="环境变量ID，可选")
    override_config = serializers.JSONField(required=False, default=dict,
                                          help_text="临时覆盖配置")

    def validate_environment_id(self, value):
        """验证环境ID是否存在"""
        if value is not None:
            try:
                ApiTestEnvironment.objects.get(id=value, IsActive=True)
            except ApiTestEnvironment.DoesNotExist:
                raise serializers.ValidationError("指定的环境不存在")
        return value


class ApiTestCodeSnippetSerializer(serializers.Serializer):
    """代码片段生成请求序列化器"""
    language = serializers.ChoiceField(
        choices=[
            ('curl', 'cURL'),
            ('python', 'Python Requests'),
            ('javascript', 'JavaScript Fetch'),
            ('java', 'Java OkHttp'),
            ('go', 'Go HTTP'),
        ],
        default='curl',
        help_text="代码语言类型"
    )
    environment_id = serializers.IntegerField(required=False, allow_null=True,
                                             help_text="环境变量ID，用于变量替换")

    def validate_environment_id(self, value):
        """验证环境ID是否存在"""
        if value is not None:
            try:
                ApiTestEnvironment.objects.get(id=value, IsActive=True)
            except ApiTestEnvironment.DoesNotExist:
                raise serializers.ValidationError("指定的环境不存在")
        return value



