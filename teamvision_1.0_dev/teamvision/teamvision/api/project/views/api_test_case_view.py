# coding=utf-8
"""
Created on 2025-09-15

@author: zhangpeng
"""

from rest_framework import generics, status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from django.shortcuts import get_object_or_404
from django.core.cache import cache

from teamvision.project.models import ApiTestCollection, ApiTestCase, ApiTestEnvironment, ApiTestHistory
from teamvision.api.project.serializer.api_test_case_serializer import (
    ApiTestCollectionSerializer, ApiTestCaseSerializer, ApiTestCollectionTestCaseTreeSerializer,
    ApiTestEnvironmentSerializer, ApiTestHistorySerializer,
    ApiTestCaseExecuteSerializer, ApiTestCodeSnippetSerializer
)
from teamvision.api.project.views.CsrfExemptSessionAuthentication import CsrfExemptSessionAuthentication


# ==================== 集合管理视图 ====================

class ApiTestCollectionListView(generics.ListCreateAPIView):
    """
    GET /api/project/{project_id}/api-test/collections/
    POST /api/project/{project_id}/api-test/collections/
    """
    serializer_class = ApiTestCollectionSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        parent = self.request.GET.get('parent', 0)
        return ApiTestCollection.objects.get_project_collections(project_id, parent)

    def perform_create(self, serializer):
        project_id = self.kwargs['project_id']
        serializer.save(project_id=project_id, creator=self.request.user.id)

    def post(self, request, *args, **kwargs):
        # 清除相关缓存
        project_id = self.kwargs['project_id']
        cache.delete(f'api_collections_{project_id}')
        return self.create(request, *args, **kwargs)


class ApiTestCollectionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET/PUT/DELETE /api/project/{project_id}/api-test/collections/{collection_id}/
    """
    serializer_class = ApiTestCollectionSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        collection_id = self.kwargs['collection_id']
        return get_object_or_404(ApiTestCollection, id=collection_id, IsActive=True)

    def perform_destroy(self, instance):
        # 软删除
        instance.IsActive = False
        instance.save()
        # 清除缓存
        cache.delete(f'api_collections_{instance.project_id}')


class ApiTestCollectionTreeView(APIView):
    """
    GET /api/project/{project_id}/api-test/collections/tree/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get(self, request, project_id):
        try:
            # 尝试从缓存获取
            cache_key = f'api_collections_tree_{project_id}'
            tree_data = cache.get(cache_key)

            if tree_data is None:
                tree_data = ApiTestCollection.objects.get_collection_tree(project_id)
                # 缓存30s
                cache.set(cache_key, tree_data, 30)

            return Response(tree_data)
        except Exception as e:
            return Response(None, status=500)


class ApiTestCollectionApiCaseTreeView(generics.ListAPIView):
    """
    GET /api/project/{project_id}/api-test/collections/tree_V2/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    serializer_class = ApiTestCollectionTestCaseTreeSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        return ApiTestCollection.objects.filter(project_id=project_id, parent=0, IsActive=True)


class ApiTestCollectionTreeLazyLoadView(APIView):
    """
    GET /api/project/{project_id}/api-test/collections/tree/lazyload/{collection_id}
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get(self, request, collection_id):
        try:
            # 尝试从缓存获取
            cache_key = f'api_collections_tree_lazyload_{collection_id}'
            tree_data = cache.get(cache_key)

            if tree_data is None:
                tree_data = ApiTestCollection.objects.get_collection_tree_lazyload(collection_id)
                # 缓存30s
                cache.set(cache_key, tree_data, 30)

            return Response(tree_data)
        except Exception as e:
            return Response(None, status=500)

        
# ==================== 测试用例管理视图 ====================

class ApiTestCaseListView(generics.ListCreateAPIView):
    """
    GET /api/project/{project_id}/api-test/cases/
    POST /api/project/{project_id}/api-test/cases/
    """
    serializer_class = ApiTestCaseSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        collection_id = self.request.GET.get('collection_id')

        if collection_id:
            return ApiTestCase.objects.get_collection_cases(collection_id)
        else:
            return ApiTestCase.objects.get_project_cases(project_id)

    def perform_create(self, serializer):
        serializer.save(creator=self.request.user.id)


class ApiTestCaseDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET/PUT/DELETE /api/project/{project_id}/api-test/cases/{case_id}/
    """
    serializer_class = ApiTestCaseSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        case_id = self.kwargs['case_id']
        return get_object_or_404(ApiTestCase, id=case_id, IsActive=True)

    def perform_destroy(self, instance):
        # 软删除
        instance.IsActive = False
        instance.save()


class ApiTestCaseExecuteView(APIView):
    """
    POST /api/project/{project_id}/api-test/cases/{case_id}/execute/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def post(self, request, project_id, case_id):
        try:
            # 验证请求数据
            serializer = ApiTestCaseExecuteSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(data=serializer.errors, status=400)

            test_case = get_object_or_404(ApiTestCase, id=case_id, IsActive=True)
            environment_id = serializer.validated_data.get('environment_id')
            override_config = serializer.validated_data.get('override_config', {})

            # 导入并使用执行器类
            from business.project.api_test_executor import ApiTestExecutor
            executor = ApiTestExecutor(test_case, environment_id, override_config)
            result = executor.execute()

            # 保存执行历史
            history = ApiTestHistory.objects.create(
                project_id=project_id,
                test_case=test_case,
                request_snapshot=executor.get_request_snapshot(),
                response_status=result.get('status_code'),
                response_headers=result.get('headers', {}),
                response_body=result.get('body', ''),
                response_time=result.get('response_time'),
                response_size=result.get('response_size'),
                test_results=result.get('test_results', []),
                is_success=result.get('is_success', True),
                error_message=result.get('error_message'),
                executor=request.user.id
            )

            if result.get('is_success', True):
                return Response(data={'history_id': history.id,'response': result},
                                status=status.HTTP_200_OK)
            else:
                return Response(data={'history_id': history.id,'response': result},
                                status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response(data={'result': None}, status=500)


# ==================== 环境管理视图 ====================

class ApiTestEnvironmentListView(generics.ListCreateAPIView):
    """
    GET /api/project/{project_id}/api-test/environments/
    POST /api/project/{project_id}/api-test/environments/
    """
    serializer_class = ApiTestEnvironmentSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        return ApiTestEnvironment.objects.get_project_environments(project_id)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        # 在列表视图中隐藏敏感变量的值
        context['hide_secrets'] = True
        return context

    def perform_create(self, serializer):
        project_id = self.kwargs['project_id']
        serializer.save(project_id=project_id, creator=self.request.user.id)


class ApiTestEnvironmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET/PUT/DELETE /api/project/{project_id}/api-test/environments/{env_id}/
    """
    serializer_class = ApiTestEnvironmentSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        env_id = self.kwargs['env_id']
        return get_object_or_404(ApiTestEnvironment, id=env_id, IsActive=True)

    def perform_destroy(self, instance):
        # 软删除
        instance.IsActive = False
        instance.save()


# ==================== 执行历史视图 ====================

class ApiTestHistoryListView(generics.ListAPIView):
    """
    GET /api/project/{project_id}/api-test/history/
    """
    serializer_class = ApiTestHistorySerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        case_id = self.request.GET.get('case_id')
        limit = int(self.request.GET.get('limit', 50))

        if case_id:
            return ApiTestHistory.objects.get_case_history(case_id, limit)
        else:
            return ApiTestHistory.objects.get_project_history(project_id, limit)


class ApiTestHistoryDetailView(generics.RetrieveAPIView):
    """
    GET /api/project/{project_id}/api-test/history/{history_id}/
    """
    serializer_class = ApiTestHistorySerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        history_id = self.kwargs['history_id']
        return get_object_or_404(ApiTestHistory, id=history_id, IsActive=True)


# ==================== 代码片段生成视图 ====================

class ApiTestCodeSnippetView(APIView):
    """
    POST /api/project/{project_id}/api-test/cases/{case_id}/code-snippet/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def post(self, request, project_id, case_id):
        try:
            # 验证请求数据
            serializer = ApiTestCodeSnippetSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({
                    'code': 1002,
                    'msg': '请求参数错误',
                    'result': serializer.errors
                }, status=400)

            test_case = get_object_or_404(ApiTestCase, id=case_id, IsActive=True)
            language = serializer.validated_data.get('language', 'curl')
            environment_id = serializer.validated_data.get('environment_id')

            # 获取环境变量
            environment = None
            if environment_id:
                environment = get_object_or_404(ApiTestEnvironment, id=environment_id, IsActive=True)

            # 导入并使用代码生成器类
            from business.project.code_snippet_generator import CodeSnippetGenerator
            generator = CodeSnippetGenerator(test_case, environment)
            code_snippet = generator.generate(language)

            return Response({
                'code': 0,
                'msg': 'success',
                'result': {
                    'language': language,
                    'code_snippet': code_snippet
                }
            })

        except Exception as e:
            return Response({
                'code': 1001,
                'msg': str(e),
                'result': None
            }, status=500)
